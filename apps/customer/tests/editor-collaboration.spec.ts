import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Editor Basic Features', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    // Add console logging for debugging
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should display basic editor functionality', async ({ page }) => {
    // Create a document using test utils
    await testUtils.createDocumentFromTemplate()

    // Wait for editor to load
    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Check for basic toolbar buttons that actually exist (using text content)
    await expect(page.locator('button:has-text("Save")')).toBeVisible()
    await expect(page.locator('button:has-text("Print")')).toBeVisible()
    await expect(page.locator('button:has-text("Export")')).toBeVisible()

    // Check for formatting buttons (these might have icons, so check if they exist)
    const boldButton = page.locator('button').filter({ hasText: /bold/i }).or(page.locator('button[aria-label*="bold"]')).or(page.locator('button[title*="bold"]'))
    const italicButton = page.locator('button').filter({ hasText: /italic/i }).or(page.locator('button[aria-label*="italic"]')).or(page.locator('button[title*="italic"]'))

    // At least one of these should be visible
    await expect(boldButton.or(italicButton)).toBeVisible()
  })

  test('should allow text editing and formatting', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Add some text
    await editor.click()
    await page.keyboard.type('This is a test document for basic editing')

    // Verify text was entered
    await expect(editor).toContainText('This is a test document for basic editing')

    // Select text
    await page.keyboard.press('ControlOrMeta+a')

    // Look for formatting buttons (they might be icons or have different attributes)
    const boldButton = page.locator('button').filter({ hasText: /bold/i }).or(page.locator('button[aria-label*="bold"]')).or(page.locator('button[title*="bold"]'))
    const italicButton = page.locator('button').filter({ hasText: /italic/i }).or(page.locator('button[aria-label*="italic"]')).or(page.locator('button[title*="italic"]'))

    // Check if formatting buttons exist
    await expect(boldButton.or(italicButton)).toBeVisible()
  })

  test('should save document content', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Add some text
    await editor.click()
    await page.keyboard.type('Content to save')

    // Click save button (using text content)
    await page.click('button:has-text("Save")')

    // Wait a moment for save to complete
    await page.waitForTimeout(1000)

    // Verify content persists
    await expect(editor).toContainText('Content to save')
  })

  test('should display document metadata', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()

    // Check if document title is displayed
    const documentTitle = page.locator('h1')
    await expect(documentTitle).toBeVisible()

    // Check if entity and run information is displayed
    await expect(page.locator('text=John Lewis')).toBeVisible()
    await expect(page.locator('text=Latest Run')).toBeVisible()
  })

  test('should have export functionality', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()

    // Check if export button exists and is clickable
    const exportButton = page.locator('button:has-text("Export")')
    await expect(exportButton).toBeVisible()
    await expect(exportButton).toBeEnabled()
  })

  test('should have print functionality', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()

    // Check if print button exists and is clickable
    const printButton = page.locator('button:has-text("Print")')
    await expect(printButton).toBeVisible()
    await expect(printButton).toBeEnabled()
  })

  test('should navigate back to documents list', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()

    // Check if back button exists
    const backButton = page.locator('button:has-text("Back")')
    await expect(backButton).toBeVisible()

    // Click back button
    await backButton.click()

    // Should navigate back to documents page
    await expect(page).toHaveURL(/\/customer\/documents$/)
  })

  test('should handle basic text formatting options', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Add some text
    await editor.click()
    await page.keyboard.type('Testing formatting options')

    // Select text
    await page.keyboard.press('ControlOrMeta+a')

    // Test that the editor has some formatting capabilities
    // Look for any formatting buttons in the toolbar
    const toolbar = page.locator('[role="toolbar"]').or(page.locator('.toolbar')).or(page.locator('[data-testid*="toolbar"]'))
    await expect(toolbar.or(page.locator('button').first())).toBeVisible()

    // Check that we can type and format text (basic functionality)
    await expect(editor).toContainText('Testing formatting options')
  })

  test('should handle list creation', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Add some text
    await editor.click()
    await page.keyboard.type('List item 1')

    // Verify text was added
    await expect(editor).toContainText('List item 1')

    // Add more content with Enter
    await page.keyboard.press('Enter')
    await page.keyboard.type('List item 2')

    // Verify both items are there
    await expect(editor).toContainText('List item 1')
    await expect(editor).toContainText('List item 2')
  })

  test('should handle table insertion', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Click in editor
    await editor.click()

    // Test table insertion button exists and is clickable
    const tableButton = page.locator('button[title="Insert Table"]')
    await expect(tableButton).toBeVisible()
    await expect(tableButton).toBeEnabled()
  })

  test('should handle image insertion', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Click in editor
    await editor.click()

    // Test image insertion button exists and is clickable
    const imageButton = page.locator('button[title="Insert Image"]')
    await expect(imageButton).toBeVisible()
    await expect(imageButton).toBeEnabled()
  })

  test('should handle link insertion', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Add some text
    await editor.click()
    await page.keyboard.type('Link text')

    // Select text
    await page.keyboard.press('ControlOrMeta+a')

    // Test link insertion button exists and is clickable
    const linkButton = page.locator('button[title="Insert Link"]')
    await expect(linkButton).toBeVisible()
    await expect(linkButton).toBeEnabled()
  })

  test('should handle undo and redo functionality', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Add some text
    await editor.click()
    await page.keyboard.type('Text to undo')

    // Verify text is there
    await expect(editor).toContainText('Text to undo')

    // Test undo/redo buttons exist (they may be disabled initially)
    await expect(page.locator('button[title="Undo"]')).toBeVisible()
    await expect(page.locator('button[title="Redo"]')).toBeVisible()
  })
})
