import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Editor Basic Features', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    // Add console logging for debugging
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });

    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should display basic editor functionality', async ({ page }) => {
    // Create a document using test utils
    await testUtils.createDocumentFromTemplate()

    // Wait for editor to load
    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Check for basic toolbar buttons that actually exist
    await expect(page.locator('button[title="Bold"]')).toBeVisible()
    await expect(page.locator('button[title="Italic"]')).toBeVisible()
    await expect(page.locator('button[title="Save"]')).toBeVisible()
    await expect(page.locator('button[title="Print"]')).toBeVisible()
    await expect(page.locator('button[title="Export"]')).toBeVisible()
  })

  test('should allow text editing and formatting', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Add some text
    await editor.click()
    await page.keyboard.type('This is a test document for basic editing')

    // Verify text was entered
    await expect(editor).toContainText('This is a test document for basic editing')

    // Select text and apply bold formatting
    await page.keyboard.press('ControlOrMeta+a')
    await page.click('button[title="Bold"]')

    // Verify bold button is active (basic check)
    const boldButton = page.locator('button[title="Bold"]')
    await expect(boldButton).toBeVisible()
  })

  test('should save document content', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Add some text
    await editor.click()
    await page.keyboard.type('Content to save')

    // Click save button
    await page.click('button[title="Save"]')

    // Wait a moment for save to complete
    await page.waitForTimeout(1000)

    // Verify content persists
    await expect(editor).toContainText('Content to save')
  })

  test('should display document metadata', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()

    // Check if document title is displayed
    const documentTitle = page.locator('h1')
    await expect(documentTitle).toBeVisible()

    // Check if entity and run information is displayed
    await expect(page.locator('text=John Lewis')).toBeVisible()
    await expect(page.locator('text=Latest Run')).toBeVisible()
  })

  test('should have export functionality', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()

    // Check if export button exists and is clickable
    const exportButton = page.locator('button[title="Export"]')
    await expect(exportButton).toBeVisible()
    await expect(exportButton).toBeEnabled()
  })

  test('should have print functionality', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()

    // Check if print button exists and is clickable
    const printButton = page.locator('button[title="Print"]')
    await expect(printButton).toBeVisible()
    await expect(printButton).toBeEnabled()
  })

  test('should navigate back to documents list', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()

    // Check if back button exists
    const backButton = page.locator('button:has-text("Back")')
    await expect(backButton).toBeVisible()

    // Click back button
    await backButton.click()

    // Should navigate back to documents page
    await expect(page).toHaveURL(/\/customer\/documents$/)
  })

  test('should handle basic text formatting options', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Add some text
    await editor.click()
    await page.keyboard.type('Testing formatting options')

    // Select text
    await page.keyboard.press('ControlOrMeta+a')

    // Test various formatting buttons exist and are clickable
    await expect(page.locator('button[title="Bold"]')).toBeEnabled()
    await expect(page.locator('button[title="Italic"]')).toBeEnabled()
    await expect(page.locator('button[title="Underline"]')).toBeEnabled()

    // Test alignment buttons
    await expect(page.locator('button[title="Align Left"]')).toBeEnabled()
    await expect(page.locator('button[title="Align Center"]')).toBeEnabled()
    await expect(page.locator('button[title="Align Right"]')).toBeEnabled()
  })

  test('should handle list creation', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })

    // Add some text
    await editor.click()
    await page.keyboard.type('List item 1')

    // Test list buttons exist and are clickable
    await expect(page.locator('button[title="Bullet List"]')).toBeEnabled()
    await expect(page.locator('button[title="Numbered List"]')).toBeEnabled()

    // Click bullet list button
    await page.click('button[title="Bullet List"]')

    // Add more list items
    await page.keyboard.press('Enter')
    await page.keyboard.type('List item 2')
  })

  test('should handle comment replies', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })
    
    // Add text and comment
    await editor.click()
    await page.keyboard.type('Text with comment thread')
    await page.keyboard.press('ControlOrMeta+a')
    
    await page.click('[data-testid="comments-button"]')
    await page.fill('textarea[placeholder*="comment"]', 'Original comment')
    await page.click('[data-testid="add-comment-button"]')
    
    // Reply to comment
    await page.click('[data-testid="reply-button"]')
    await page.fill('textarea[placeholder*="reply"]', 'This is a reply')
    await page.click('[data-testid="reply-button"]')
    
    // Should show both comments
    await expect(page.locator('text=Original comment')).toBeVisible()
    await expect(page.locator('text=This is a reply')).toBeVisible()
    
    // Should show thread structure
    await expect(page.locator('[data-testid="comment-thread"]')).toBeVisible()
  })

  test('should resolve and unresolve comments', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })
    
    // Add text and comment
    await editor.click()
    await page.keyboard.type('Text with resolvable comment')
    await page.keyboard.press('ControlOrMeta+a')
    
    await page.click('[data-testid="comments-button"]')
    await page.fill('textarea[placeholder*="comment"]', 'Comment to resolve')
    await page.click('[data-testid="add-comment-button"]')
    
    // Resolve comment
    await page.click('[data-testid="resolve-button"]')
    
    // Comment should be marked as resolved
    await expect(page.locator('[data-testid="resolved-comment"]')).toBeVisible()
    
    // Highlight should be removed from editor
    await expect(editor.locator('.comment-highlight')).not.toBeVisible()
    
    // Unresolve comment
    await page.click('[data-testid="unresolve-button"]')
    
    // Comment should be active again
    await expect(page.locator('[data-testid="resolved-comment"]')).not.toBeVisible()
    await expect(editor.locator('.comment-highlight')).toBeVisible()
  })

  test('should handle collaboration settings', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()
    
    // Click settings button
    await page.click('button[title*="Settings"]')
    
    // Should show settings dialog or panel
    await expect(page.locator('[data-testid="collaboration-settings"]')).toBeVisible()
    
    // Should have permission settings
    await expect(page.locator('text=Document Permissions')).toBeVisible()
    await expect(page.locator('text=Public')).toBeVisible()
    await expect(page.locator('text=Private')).toBeVisible()
    
    // Should have notification settings
    await expect(page.locator('text=Notifications')).toBeVisible()
    await expect(page.locator('input[type="checkbox"]')).toBeVisible()
  })

  test('should handle real-time cursor positions', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })
    
    // Add content
    await editor.click()
    await page.keyboard.type('Content for cursor tracking')
    
    // Move cursor to different position
    await page.keyboard.press('Home')
    
    // Should show cursor position indicator
    await expect(page.locator('[data-testid="cursor-indicator"]')).toBeVisible()
    
    // Cursor should have user color
    const cursor = page.locator('[data-testid="cursor-indicator"]')
    const cursorStyle = await cursor.getAttribute('style')
    expect(cursorStyle).toContain('color')
  })
})
