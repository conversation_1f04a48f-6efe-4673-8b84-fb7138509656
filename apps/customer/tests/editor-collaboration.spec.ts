import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Editor Collaboration Features', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    // Add console logging
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });
    
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should display collaboration toolbar', async ({ page }) => {
    // Create a document using test utils
    await testUtils.createDocumentFromTemplate('Blank Document')
    
    // Check collaboration toolbar is visible
    await expect(page.locator('[data-testid="collaboration-toolbar"]')).toBeVisible({ timeout: 10000 })
  })

  test('should open and close comments panel', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()
    
    // Open comments panel
    await page.click('[data-testid="comments-button"]')

    // Check comments panel opens
    await expect(page.locator('text=Comments')).toBeVisible()
    await expect(page.locator('text=No comments yet')).toBeVisible()

    // Close panel
    await page.click('[data-testid="comments-button"]')
    await expect(page.locator('[data-testid="comments-panel"]')).not.toBeVisible()
  })

  test('should add comments to selected text', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })
    
    // Add some text
    await editor.click()
    await page.keyboard.type('This text will have a comment')
    
    // Select text
    await page.keyboard.press('ControlOrMeta+a')
    
    // Open comments panel
    await page.click('[data-testid="comments-button"]')
    await expect(page.locator('[data-testid="comments-panel"]')).toBeVisible()
    
    // Should show comment form at bottom of panel
    await expect(page.locator('textarea[placeholder*="comment"]')).toBeVisible()
    
    // Type comment
    await page.fill('textarea[placeholder*="comment"]', 'This is a test comment')
    
    // Submit comment (button text is "Add Comment" not "Post")
    await page.click('[data-testid="add-comment-button"]')
    
    // Comment should appear in panel
    await expect(page.locator('text=This is a test comment')).toBeVisible()
    
    // Text should be highlighted in editor
    await expect(editor.locator('.comment-highlight')).toBeVisible()
  })

  test('should open and close history panel', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()
    
    // Open history panel
    await page.click('[data-testid="history-button"]')

    // Check history panel opens (text is "Version History" not "Document History")
    await expect(page.locator('[data-testid="history-panel"]')).toBeVisible()

    // Should show version history
    await expect(page.locator('[data-testid="version-item"]')).toBeVisible()

    // Close panel
    await page.click('[data-testid="history-button"]')
    await expect(page.locator('[data-testid="history-panel"]')).not.toBeVisible()
  })

  test('should display document versions in history', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })
    
    // Make some changes to create history
    await editor.click()
    await page.keyboard.type('First version of content')
    
    // Wait for auto-save
    await expect(page.locator('text=Saved')).toBeVisible({ timeout: 10000 })
    
    // Make more changes
    await page.keyboard.press('Enter')
    await page.keyboard.type('Second version with more content')
    
    // Wait for auto-save
    await expect(page.locator('text=Saved')).toBeVisible({ timeout: 10000 })
    
    // Open history panel
    await page.click('[data-testid="history-button"]')
    await expect(page.locator('[data-testid="history-panel"]')).toBeVisible()
    
    // Should show multiple versions
    const versionItems = page.locator('[data-testid="version-item"]')
    await expect(versionItems).toHaveCount(2, { timeout: 5000 })
    
    // Each version should have timestamp
    await expect(page.locator('[data-testid="version-timestamp"]')).toHaveCount(2)
  })

  test('should open and close share panel', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()
    
    // Open share panel
    await page.click('button:has-text("Share")')

    // Check share panel opens
    await expect(page.locator('[data-testid="share-panel"]')).toBeVisible()
    
    // Check panel content
    await expect(page.locator('input[placeholder*="email"]')).toBeVisible()
    
    // Close panel (try same button again first, then close button if exists)
    await page.click('button:has-text("Share")')
    await expect(page.locator('[data-testid="share-panel"]')).not.toBeVisible()
  })

  test('should handle share invitations', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()
    
    // Open share panel
    await page.click('button:has-text("Share")')
    await expect(page.locator('[data-testid="share-panel"]')).toBeVisible()
    
    // Enter email
    await page.fill('input[placeholder*="email"]', '<EMAIL>')
    
    // Select permission level
    await page.click('[data-testid="permission-select"]')
    await page.click('text=Can edit')
    
    // Send invite (button might have different text)
    await page.click('button:has-text("Add Collaborator"), button:has-text("Send Invite"), button:has-text("Invite")')
    
    // Should show success message (might be toast notification)
    await expect(page.locator('text=Invitation sent, text=Collaborator added, text=User invited')).toBeVisible({ timeout: 5000 })
    
    // Email should appear in collaborators list
    await expect(page.locator('text=<EMAIL>')).toBeVisible()
  })

  test('should show active collaborators', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()
    
    // Check for collaborator avatars in toolbar
    await expect(page.locator('[data-testid="collaborator-avatar"]')).toBeVisible()
    
    // Should show current user
    await expect(page.locator('[data-testid="current-user-avatar"]')).toBeVisible()
    
    // Hover over avatar to see user info
    await page.hover('[data-testid="current-user-avatar"]')
    await expect(page.locator('[data-testid="user-tooltip"]')).toBeVisible({ timeout: 2000 })
  })

  test('should handle comment replies', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })
    
    // Add text and comment
    await editor.click()
    await page.keyboard.type('Text with comment thread')
    await page.keyboard.press('ControlOrMeta+a')
    
    await page.click('[data-testid="comments-button"]')
    await page.fill('textarea[placeholder*="comment"]', 'Original comment')
    await page.click('[data-testid="add-comment-button"]')
    
    // Reply to comment
    await page.click('[data-testid="reply-button"]')
    await page.fill('textarea[placeholder*="reply"]', 'This is a reply')
    await page.click('[data-testid="reply-button"]')
    
    // Should show both comments
    await expect(page.locator('text=Original comment')).toBeVisible()
    await expect(page.locator('text=This is a reply')).toBeVisible()
    
    // Should show thread structure
    await expect(page.locator('[data-testid="comment-thread"]')).toBeVisible()
  })

  test('should resolve and unresolve comments', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })
    
    // Add text and comment
    await editor.click()
    await page.keyboard.type('Text with resolvable comment')
    await page.keyboard.press('ControlOrMeta+a')
    
    await page.click('[data-testid="comments-button"]')
    await page.fill('textarea[placeholder*="comment"]', 'Comment to resolve')
    await page.click('[data-testid="add-comment-button"]')
    
    // Resolve comment
    await page.click('[data-testid="resolve-button"]')
    
    // Comment should be marked as resolved
    await expect(page.locator('[data-testid="resolved-comment"]')).toBeVisible()
    
    // Highlight should be removed from editor
    await expect(editor.locator('.comment-highlight')).not.toBeVisible()
    
    // Unresolve comment
    await page.click('[data-testid="unresolve-button"]')
    
    // Comment should be active again
    await expect(page.locator('[data-testid="resolved-comment"]')).not.toBeVisible()
    await expect(editor.locator('.comment-highlight')).toBeVisible()
  })

  test('should handle collaboration settings', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    await testUtils.waitForEditor()
    
    // Click settings button
    await page.click('button[title*="Settings"]')
    
    // Should show settings dialog or panel
    await expect(page.locator('[data-testid="collaboration-settings"]')).toBeVisible()
    
    // Should have permission settings
    await expect(page.locator('text=Document Permissions')).toBeVisible()
    await expect(page.locator('text=Public')).toBeVisible()
    await expect(page.locator('text=Private')).toBeVisible()
    
    // Should have notification settings
    await expect(page.locator('text=Notifications')).toBeVisible()
    await expect(page.locator('input[type="checkbox"]')).toBeVisible()
  })

  test('should handle real-time cursor positions', async ({ page }) => {
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    await expect(editor).toBeVisible({ timeout: 10000 })
    
    // Add content
    await editor.click()
    await page.keyboard.type('Content for cursor tracking')
    
    // Move cursor to different position
    await page.keyboard.press('Home')
    
    // Should show cursor position indicator
    await expect(page.locator('[data-testid="cursor-indicator"]')).toBeVisible()
    
    // Cursor should have user color
    const cursor = page.locator('[data-testid="cursor-indicator"]')
    const cursorStyle = await cursor.getAttribute('style')
    expect(cursorStyle).toContain('color')
  })
})
