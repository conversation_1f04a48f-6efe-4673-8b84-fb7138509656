import { expect, test } from '@playwright/test'
import { TestUtils } from './helpers/tests/test-utils'

test.describe('Editor Auto-save Functionality', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    page.on('console', msg => {
      console.log(`Browser console: ${msg.type()}: ${msg.text()}`);
    });
    
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should auto-save content changes', async ({ page }) => {
    console.log('Starting auto-save test')
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    
    // Check initial save button state shows "Save" (not saving)
    const saveButton = page.locator('[data-testid="save-button"]')
    await expect(saveButton).toBeVisible({ timeout: 10000 })
    
    // Make changes
    await editor.click()
    await page.keyboard.type('Testing auto-save functionality')
    
    // According to useSupabaseAutoSave, default save interval is 5 seconds
    // Wait for autosave to trigger (5 seconds + buffer)
    await page.waitForTimeout(6000)
    
    // Should show saving status in save button briefly
    // Note: The saving state might be very quick, so we check for either state
    const savingButton = page.locator('[data-testid="save-button"]:has-text("Saving...")')
    const savedButton = page.locator('[data-testid="save-button"]:has-text("Save")')
    
    // Check if we catch the saving state or if it already completed
    const isSaving = await savingButton.isVisible()
    if (isSaving) {
      console.log('Caught saving state')
      await expect(savedButton).toBeVisible({ timeout: 10000 })
    } else {
      console.log('Save completed quickly, checking for timestamp')
      // If save was too quick, check for last saved timestamp
      await expect(page.locator('text=/Last saved:/')).toBeVisible({ timeout: 5000 })
    }
  })

  test('should debounce rapid changes', async ({ page }) => {
    console.log('Starting debounce test')
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()

    // Initially no timestamp
    await expect(page.locator('text=/Last saved:/')).not.toBeVisible()
    
    await editor.click()

    // Type rapidly
    await page.keyboard.type('Rapid')
    await page.waitForTimeout(100)
    await page.keyboard.type(' typing')
    await page.waitForTimeout(100)
    await page.keyboard.type(' test')
    
    // Should not save immediately - wait less than debounce period
    await page.waitForTimeout(3000)
    
    // Should not be saving yet
    const savingButton = page.locator('[data-testid="save-button"]:has-text("Saving...")')
    await expect(savingButton).not.toBeVisible()
    
    // Wait for the full debounce period (5 seconds total from last keystroke)
    await page.waitForTimeout(3000)
    
    // Now it should save
    await expect(page.locator('text=/Last saved:/')).toBeVisible({ timeout: 10000 })
  })

  test('should handle manual save', async ({ page }) => {
    console.log('Starting manual save test')
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    
    await editor.click()
    await page.keyboard.type('Content for manual save')
    
    // Click manual save button
    const saveButton = page.locator('[data-testid="save-button"]')
    await saveButton.click()
    
    // Should show saving status briefly or complete quickly
    const savingButton = page.locator('[data-testid="save-button"]:has-text("Saving...")')
    
    // Check if we can catch the saving state
    try {
      await expect(savingButton).toBeVisible({ timeout: 2000 })
      console.log('Caught saving state during manual save')
      // Wait for save to complete
      await expect(savingButton).not.toBeVisible({ timeout: 10000 })
    } catch {
      console.log('Manual save completed quickly')
    }
    
    // Should show save button and timestamp
    await expect(page.locator('[data-testid="save-button"]')).toBeVisible()
    await expect(page.locator('text=/Last saved:/')).toBeVisible({ timeout: 5000 })
  })

  test('should show last saved timestamp', async ({ page }) => {
    console.log('Starting timestamp test')
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    
    // Make changes
    await editor.click()
    await page.keyboard.type('Content with timestamp')
    
    // Manually save to trigger timestamp
    const saveButton = page.locator('[data-testid="save-button"]')
    await saveButton.click()
    
    // Should show timestamp text (format: "Last saved: {time}")
    const timestampText = page.locator('text=/Last saved:/')
    await expect(timestampText).toBeVisible({ timeout: 5000 })
    
    // Timestamp should contain time format
    const timestamp = await timestampText.textContent()
    console.log('Timestamp text:', timestamp)
    expect(timestamp).toMatch(/Last saved: \d+:\d+/)
  })

  test('should preserve content in editor', async ({ page }) => {
    console.log('Starting content preservation test')
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    
    // Type content
    await editor.click()
    await page.keyboard.type('Content that should persist')
    
    // Content should be visible in editor
    await expect(editor).toContainText('Content that should persist')
    
    // Wait for potential autosave (longer than debounce period)
    await page.waitForTimeout(6000)
    
    // Content should still be there after autosave
    await expect(editor).toContainText('Content that should persist')
    
    console.log('Content preservation test completed')
  })

  test('should maintain editor functionality during typing', async ({ page }) => {
    console.log('Starting editor functionality test')
    const documentId = await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()
    
    await editor.click()
    
    // Type various content types
    await page.keyboard.type('# Heading\n\nParagraph with **bold** text.\n\n- List item 1\n- List item 2')
    
    // Wait a moment for content to render
    await page.waitForTimeout(1000)
    
    // Verify content structure is maintained
    await expect(editor.locator('h1')).toContainText('Heading')
    await expect(editor.locator('strong')).toContainText('bold')
    await expect(editor.locator('ul li')).toContainText('List item 1')
    
    // Save button should be functional
    const saveButton = page.locator('[data-testid="save-button"]')
    await expect(saveButton).toBeVisible()
    await expect(saveButton).toBeEnabled()
    
    console.log('Editor functionality test completed')
  })
})
