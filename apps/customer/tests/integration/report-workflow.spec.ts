import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('Report Workflow Integration', () => {
  let testUtils: TestUtils;

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page);
    await testUtils.login();
  });

  test('complete report creation and editing workflow', async ({ page }) => {
    // Step 1: Create document from template
    const documentId = await testUtils.createDocumentFromTemplate('EKO Report')
    
    // Step 2: Verify initial structure
    expect(await testUtils.countComponents('report-summary')).toBeGreaterThan(0);
    expect(await testUtils.countComponents('report-group')).toBeGreaterThan(0);
    expect(await testUtils.countComponents('report-section')).toBeGreaterThan(0);
    
    // Step 3: Wait for initial content loading
    await testUtils.waitForComponentLoading('.report-section');
    
    // Step 4: Add custom content
    await testUtils.typeInEditor('This is a custom introduction to the ESG report.');
    
    // Step 5: Add new report section
    await testUtils.addReportComponent('section', {
      id: 'custom-section',
      title: 'Custom Analysis',
      prompt: 'Provide analysis on custom metrics'
    });
    
    // Step 6: Verify new section was added
    await testUtils.checkComponentExists('custom-section');
    
    // Step 7: Configure existing component
    await testUtils.openComponentConfig('.report-section');
    await testUtils.fillComponentConfig({
      prompt: 'Updated prompt for better analysis'
    });
    await testUtils.confirmComponentConfig();
    
    // Step 8: Test component actions
    await testUtils.performComponentAction('lock', '.report-section');
    await testUtils.checkComponentState('locked', '.report-section');
    
    // Step 9: Test refresh functionality
    await testUtils.performComponentAction('refresh', '.report-section:not(.locked)');
    await testUtils.waitForComponentLoading('.report-section:not(.locked)');
    
    // Step 10: Verify content persistence
    await testUtils.goToDocuments();
    await page.goto(`/customer/documents/${documentId}`);
    await testUtils.checkEditorContent('This is a custom introduction to the ESG report.');
    await testUtils.checkComponentExists('custom-section');
    
    // Step 11: Check for any errors
    await testUtils.checkForErrors();
  });

  test('report summary dependency workflow', async ({ page }) => {
    // Create document with template
    await testUtils.createDocumentFromTemplate('EKO Report')
    
    // Wait for sections to load
    await testUtils.waitForComponentLoading('.report-section');
    
    // Add a new summary that depends on existing sections
    await testUtils.addReportComponent('summary', {
      id: 'custom-summary',
      title: 'Custom Summary',
      prompt: 'Summarize the key findings'
    });
    
    // Configure summary dependencies
    await testUtils.openComponentConfig('.report-summary[data-id="custom-summary"]');
    
    // Select components to summarize (this would depend on the UI implementation)
    await page.click('text=Select components to summarize');
    await page.click('text=environmental-risks');
    await page.click('text=social-risks');
    
    await testUtils.confirmComponentConfig();
    
    // Wait for summary to generate
    await testUtils.waitForComponentLoading('.report-summary[data-id="custom-summary"]');
    
    // Verify summary was created
    await testUtils.checkComponentExists('custom-summary');
  });

  test('error handling and recovery', async ({ page }) => {
    // Mock API errors for testing
    await testUtils.mockApiError('/api/report/entity');

    // Create document
    await testUtils.createDocumentFromTemplate('EKO Report')
    
    // Components should handle errors gracefully
    await expect(page.locator('.error, [role="alert"]')).toBeVisible({ timeout: 10000 });
    
    // Remove the mock to test recovery
    await page.unroute('**/api/report/entity**');
    
    // Try to refresh a component
    await testUtils.performComponentAction('refresh', '.report-section');
    
    // Should recover and load content
    await testUtils.waitForComponentLoading('.report-section');
  });

  test('collaborative editing simulation', async ({ page, context }) => {
    // Create document
    const documentId = await testUtils.createDocumentFromTemplate('EKO Report')
    
    // Simulate another user opening the same document
    const page2 = await context.newPage();
    const testUtils2 = new TestUtils(page2);
    await testUtils2.login('<EMAIL>', 'demo');
    await page2.goto(`/customer/documents/${documentId}`);
    
    // User 1 adds content
    await testUtils.typeInEditor('User 1 content');
    await testUtils.waitForAutoSave();
    
    // User 2 should see the content
    await testUtils2.checkEditorContent('User 1 content');
    
    // User 2 adds content
    await testUtils2.typeInEditor(' and User 2 content');
    await testUtils2.waitForAutoSave();
    
    // User 1 should see both contents
    await testUtils.checkEditorContent('User 1 content and User 2 content');
    
    await page2.close();
  });

  test('performance with large documents', async ({ page }) => {
    // Create document
    await testUtils.createDocumentFromTemplate('EKO Report')
    
    // Add multiple components to test performance
    for (let i = 0; i < 5; i++) {
      await testUtils.addReportComponent('section', {
        id: `perf-section-${i}`,
        title: `Performance Section ${i}`,
        prompt: `Analysis for section ${i}`
      });
    }
    
    // Verify all components were added
    for (let i = 0; i < 5; i++) {
      await testUtils.checkComponentExists(`perf-section-${i}`);
    }
    
    // Test that the page remains responsive
    await testUtils.typeInEditor('Performance test content');
    await expect(page.locator('text=Performance test content')).toBeVisible();
    
    // Check loading times are reasonable
    const startTime = Date.now();
    await testUtils.waitForComponentLoading('.report-section');
    const loadTime = Date.now() - startTime;
    
    // Should load within 30 seconds
    expect(loadTime).toBeLessThan(30000);
  });

  test('export functionality', async ({ page }) => {
    // Create and populate document
    await testUtils.createDocumentFromTemplate('EKO Report')
    await testUtils.waitForComponentLoading('.report-section');
    await testUtils.typeInEditor('Export test content');
    
    // Test export (if implemented)
    try {
      const download = await testUtils.exportDocument('pdf');
      expect(download.suggestedFilename()).toMatch(/\.pdf$/);
    } catch (error) {
      // Export functionality might not be implemented yet
      console.log('Export functionality not available:', error instanceof Error ? error.message : String(error));
    }
  });

  test('accessibility compliance', async ({ page }) => {
    // Create document
    await testUtils.createDocumentFromTemplate('EKO Report')
    
    // Check for basic accessibility features
    const buttonCount = await page.locator('[role="button"]').count();
    expect(buttonCount).toBeGreaterThan(0);
    await expect(page.locator('[role="dialog"]')).toHaveCount(0); // No open dialogs initially
    
    // Open a dialog and check accessibility
    await testUtils.openComponentConfig('.report-section');
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await expect(page.locator('[aria-label="Close"]')).toBeVisible();
    
    // Check keyboard navigation
    await page.keyboard.press('Tab');
    await page.keyboard.press('Escape');
    await expect(page.locator('[role="dialog"]')).not.toBeVisible();
  });

  test('mobile responsiveness', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    // Create document
    await testUtils.createDocumentFromTemplate('EKO Report')
    
    // Check that components are still visible and functional
    await expect(page.locator('.report-section')).toBeVisible();
    await expect(page.locator('.report-group')).toBeVisible();
    
    // Test that menus work on mobile
    const reportSection = page.locator('.report-section').first();
    const menuTrigger = reportSection.locator('button[role="button"]').last();
    await menuTrigger.click();
    await expect(page.locator('text=Configure')).toBeVisible();
  });

  test('data persistence across sessions', async ({ page, context }) => {
    // Create document and add content
    const documentId = await testUtils.createDocumentFromTemplate('EKO Report')
    await testUtils.typeInEditor('Persistence test content');
    await testUtils.waitForAutoSave();
    
    // Close browser and reopen
    await page.close();
    const newPage = await context.newPage();
    const newTestUtils = new TestUtils(newPage);
    await newTestUtils.login();
    
    // Navigate to the same document
    await newPage.goto(`/customer/documents/${documentId}`);
    
    // Verify content persisted
    await newTestUtils.checkEditorContent('Persistence test content');
  });
});
